@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.b-accordion {
	--icon-width: clamp(2rem, calc(32 / 1464 * 100vw), 3.2rem);
	--icon-right: clamp(1.2rem, calc(37 / 1464 * 100vw), 3.7rem);
	$s: &;
	$gap: clamp(variables.$row-main-gutter, calc(33 / 1464 * 100vw), 3.3rem);
	&__item {
		margin: 0 0 1.3rem;
		border: 0.1rem solid variables.$color-bd;
		&:last-child {
			margin: 0;
		}
	}
	&__head {
		display: flex;
		list-style: none;
		cursor: pointer;
		-webkit-tap-highlight-color: transparent;
		&::marker,
		&::-webkit-details-marker {
			display: none;
		}
	}
	&__img {
		flex: 0 0 auto;
		width: 33.3%;
		max-width: 32.6rem;
	}
	&__text {
		display: block;
		flex: 1 1 auto;
		align-self: center;
		padding: $gap;
	}
	&__name {
		position: relative;
		display: block;
		padding-right: calc(var(--icon-width) + var(--icon-right));
		font-size: clamp(1.8rem, calc(32 / 1416 * 100vw), 3.2rem);
		&::before,
		&::after {
			content: '';
			position: absolute;
			top: 50%;
			right: 0;
			width: var(--icon-width);
			height: 0.2rem;
			background: variables.$color-text;
			transform: translateY(-50%);
		}
		&::after {
			transform: translateY(-50%) rotate(90deg);
			transition: transform variables.$t;
		}
	}
	&__pre,
	&__after {
		display: none;
	}
	&__content {
		padding: $gap;
		> .grid {
			max-width: 100%;
		}
	}
	&__radios {
		display: flex;
		gap: 3rem;
		flex-wrap: wrap;
	}
	&__btn--list {
		display: none;
	}

	// MODIF
	&__item--w-img > &__content {
		padding: variables.$row-main-gutter;
	}
	&--light {
		--icon-right: 0;
		#{$s}__item {
			margin: 0 0 -0.1rem;
			border-width: 0.1rem 0;
		}
		#{$s}__text {
			padding: 2.4rem 0;
		}
		#{$s}__content {
			padding: 0 0 3.6rem;
		}
	}
	&__item--w-agree &__content {
		display: none;
	}

	// STATES
	&:has(a[href*='type=print']) .b-file-gallery__btn--online,
	&:has(a[href*='type=online']) .b-file-gallery__btn--print {
		display: none;
	}
	&__item[open] > &__head &__name::after {
		transform: translateY(-50%);
	}
	&__item--w-agree > &__content {
		display: block;
	}
	&__item--w-agree[open] > &__head &__after {
		display: block;
	}
	&__item--w-agree[open] > &__head &__text {
		padding-bottom: 0;
	}
	&__item.is-initialized {
		#{$s}__btn--gallery {
			display: none;
		}
		#{$s}__btn--list {
			display: block;
		}
	}

	// MQ
	@media (config.$md-down) {
		&__item--w-agree > &__head {
			flex-direction: column;
			#{$s}__img {
				width: 100%;
				max-width: none;
			}
			#{$s}__text {
				align-self: stretch;
			}
		}
	}
	@media (config.$xl-up) {
		&__item--w-img > &__content {
			margin-left: min(33.3%, 32.6rem);
			padding: variables.$row-main-gutter variables.$row-main-gutter 6rem;
		}
		&__item--w-agree > &__content {
			padding: 11.6rem variables.$row-main-gutter variables.$row-main-gutter;
		}
	}
}

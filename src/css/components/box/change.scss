@use 'config';
@use 'base/variables';

.b-change {
	$defaultPosition: 50%;
	display: flex;

	&__holder {
		width: 100%;
	}

	&__before,
	&__after {
		position: absolute;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		background-color: white;
		background-position: center;
		background-repeat: no-repeat;
		background-size: cover;
		overflow: hidden;
		pointer-events: none;
		user-select: none;
	}
	&__before {
		width: $defaultPosition;
	}

	&__img {
		min-width: 100%;
		max-width: none;
		height: 100%;
		object-fit: cover;
		aspect-ratio: 2/1;
	}

	&__handle {
		position: absolute;
		top: 50%;
		left: $defaultPosition;
		display: flex;
		justify-content: center;
		align-items: center;
		width: 2rem;
		height: 4rem;
		border: 1px solid variables.$color-black;
		border-radius: 1.4rem;
		background-color: variables.$color-white;
		transform: translate(-50%, -50%);
		cursor: grab;
		pointer-events: auto;
		&::before {
			content: ' ';
			position: absolute;
			left: 50%;
			width: 0.2rem;
			height: 500rem;
			margin-left: -0.1rem;
			background: variables.$color-white;
			pointer-events: none;
		}
		&::after {
			content: '';
			position: absolute;
			width: 5rem;
			height: 5rem;
		}

		> .icon-svg {
			transform: scale(0.7);
		}
	}

	&__label {
		position: absolute;
		top: 0.8rem;
		right: 1.2rem;
		color: variables.$color-white;
		font-size: 1.6rem;
	}
	&__before &__label {
		right: auto;
		left: 1.2rem;
	}

	.hoverevents &__handle.is-scrolling:hover {
		cursor: grabbing;
	}

	// STATES
	&__handle.is-scrolling {
		cursor: grabbing;
	}

	@media (config.$md-up) {
		&__label {
			top: 3rem;
			right: 3rem;
			font-size: 3.6rem;
		}
		&__before &__label {
			left: 3rem;
		}

		&__handle {
			width: 3.6rem;
			height: 7.2rem;
			border-radius: 2.8rem;
			> .icon-svg {
				transform: scale(1);
			}
		}
	}
}

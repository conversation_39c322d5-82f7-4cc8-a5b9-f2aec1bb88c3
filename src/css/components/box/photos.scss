@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';
@use 'base/mixins';

.b-photos {
	&__cell--text {
		align-self: flex-end;
	}
	&__text {
		margin: 0;
		font-size: 1.6rem;
		line-height: 1.3;
	}
	&__img {
		margin: 0;
	}
	&__img + &__text {
		margin-top: 2.4rem;
	}

	// MQ
	@media (config.$md-down) {
		&__cell--odd {
			margin-left: auto;
		}
	}
	@media (config.$md-up) {
		&__text {
			margin-bottom: -0.3rem;
		}

		// MODIF
		&__row--right .grid {
			flex-direction: row-reverse;
		}
		&__row--2-vertical-left-horizontal-right .grid {
			justify-content: flex-start;
			.grid__cell:last-child {
				margin-left: auto;
			}
		}
		&__row--2-vertical-right-horizontal-left .grid {
			flex-direction: row-reverse;
			justify-content: flex-end;
			.grid__cell:last-child {
				margin-right: auto;
			}
		}
		&__row--2-vertical .grid {
			justify-content: center;
		}
		&__row--right &__text,
		&__row--2-vertical-right-horizontal-left &__text {
			text-align: right;
		}
	}
	@media (config.$lg-up) {
		&__row--vertical-small-big &__cell:nth-child(2) &__text {
			position: absolute;
			bottom: 0;
			left: 0;
			text-align: right;
			transform: translateX(-100%) translateX(calc(variables.$grid-gutter * -1));
		}
		&__row--vertical-big-small &__cell:nth-child(1) &__text {
			position: absolute;
			right: 0;
			bottom: 0;
			max-width: percentage(calc(4 / 12));
			transform: translateX(100%) translateX(calc(variables.$grid-gutter));
		}
		&__row--vertical-big-small &__cell:nth-child(2) &__text {
			text-align: right;
		}
	}
}

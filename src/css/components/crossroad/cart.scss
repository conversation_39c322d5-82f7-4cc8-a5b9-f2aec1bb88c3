@use 'config';
@use 'base/variables';
@use 'base/functions';
@use 'base/extends';

.c-cart {
	font-size: 1.6rem;
	&__row {
		display: flex;
		gap: 2.4rem;
		align-items: center;
		padding: 1rem 0;
		border-top: 2px solid #808080;
		&:last-child {
			border-bottom: 2px solid #808080;
		}
	}
	&__item {
		&--image {
			flex: 0 0 95px;
		}
		&--name {
			flex: 0 1 auto;
			font-weight: 500;
		}
		&--number {
			flex: 0 0 100px;
			margin-left: auto;
			.inp-text {
				border-radius: 50px;
				text-align: center;
			}
		}
		&--price {
			flex: 0 0 100px;
			font-weight: 500;
		}
		&--remove {
			flex: 0 0 50px;
			a {
				display: block;
				text-align: center;
			}
			.icon-svg {
				width: 20px;
				color: #7a736c;
			}
		}
	}
	&__image {
		img {
			display: inline-block;
			vertical-align: middle;
			max-width: 100%;
			height: auto;
		}
	}

	@media (config.$xl-down) {
		&__row {
			flex-wrap: wrap;
		}
		&__item {
			&--name {
				flex: 0 1 calc(100% - 120px);
			}
		}
	}
}

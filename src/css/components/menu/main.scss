/* stylelint-disable declaration-no-important */
@use 'config';
@use 'base/variables';
@use 'base/extends';
@use 'base/functions';
@use 'base/mixins';

.m-main {
	$s: &;
	&__list {
		@extend %reset-ul;
	}
	&__item {
		@extend %reset-ul-li;
		position: relative;
	}
	&__link {
		display: flex;
		align-items: center;
		text-decoration: none;
	}

	// VARIANTS
	&__btn--sub {
		display: none;
	}

	// STATES
	&__link.is-active,
	&__link.is-selected {
		color: variables.$color-hover;
	}

	// MQ
	@media (config.$xl-down) {
		&__list {
			position: absolute;
			top: 100%;
			right: 0;
			left: 0;
			height: calc(100dvh - var(--header-height));
			padding: 7rem variables.$row-main-gutter 2.4rem;
			background: variables.$color-white;
			overflow-x: hidden;
			overflow-y: auto;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
		}
		&__link {
			min-height: 7.2rem;
			border-top: 0.1rem solid variables.$color-bd;
		}
		&__item:last-child &__link {
			border-bottom: 0.1rem solid variables.$color-bd;
		}
		&__item.is-sub-menu-open:last-child &__link {
			border-bottom: none;
		}
		&__item.is-sub-menu-open:last-child {
			border-bottom: 0.1rem solid variables.$color-bd;
		}

		// VARIANTS
		&__link--btn {
			position: relative;
			padding-right: 3.3rem;
		}
		&__btn--sub {
			@include mixins.button-reset;
			position: absolute;
			top: 2.4rem;
			right: 0.4rem;
			width: 2.5rem;
			height: 2.5rem;
			outline: none;
			span {
				position: absolute;
				background-color: variables.$color-black;
				&:first-child {
					top: 1.2rem;
					left: 0;
					width: 2.5rem;
					height: 0.1rem;
				}
				&:last-child {
					top: 0;
					left: 1.2rem;
					width: 0.1rem;
					height: 2.5rem;
				}
			}
		}
		&__list--sub {
			position: relative;
			top: 0;
			right: auto;
			left: 0;
			display: none;
			flex-direction: column;
			height: auto;
			padding: 0 1rem 1rem;
			transition: none;
		}
		&__link--sub {
			min-height: auto;
			padding: 1rem 0;
			border: none !important;
		}

		// STATES
		.is-menu-open & {
			#{$s} {
				&__list {
					visibility: visible;
					opacity: 1;
				}
			}
		}
		.is-sub-menu-open &__list--sub {
			display: flex;
		}
		.is-sub-menu-open &__btn--sub {
			span:last-child {
				display: none;
			}
		}
	}
	@media (config.$xl-up) {
		&__btn {
			display: none;
		}
		&__list {
			display: flex;
		}
		&__item {
			position: relative;
			flex: 0 1 auto;
		}
		&__link {
			padding: 0 functions.rwd(false, 1.2rem);
		}

		// VARIANTS
		&__list--sub {
			position: absolute;
			top: 100%;
			left: 0;
			flex-direction: column;
			min-width: 15rem;
			padding: 1rem 0;
			border-radius: 1rem;
			background-color: variables.$color-white;
			visibility: hidden;
			opacity: 0;
			transition: opacity variables.$t, visibility variables.$t;
		}
		&__item--sub {
			flex: 0 1 100%;
		}
		&__link--sub {
			padding-top: 0.3rem !important;
			padding-bottom: 0.3rem !important;
			font-size: 1.6rem;
		}
		.hoverevents &__item:hover {
			#{$s}__list--sub {
				visibility: visible;
				opacity: 1;
			}
		}
	}
	@media (config.$xxl-up) {
		&__link {
			padding: 0 functions.rwd(false, 1.8rem);
		}
	}
}

/* stylelint-disable order/order */
@use 'base/variables';

// Spacing
@function spacing($depth: '0') {
	$size: map-get(variables.$utils-spacing, $depth);
	@return $size;
}

@function strip-rem($number) {
	@if type-of($number) == 'number' and not unitless($number) {
		@return calc($number / 1rem);
	}

	@return $number;
}

// $min - minimální hraniční hodnota [rem / px]
// $value - relativn<PERSON> hodn<PERSON>, odpovídající velikosti pro desktop [rem]
// $base - hraniční rozmě<PERSON> ok<PERSON>, pro kterou bude velikost přepočítávána a po které už nebude růst [rem]
@function rwd($min, $value, $base: variables.$row-main-width-unitless) {
	$unitless-value: strip-rem($value);
	$rem-value: #{$unitless-value + 'rem'};

	$value-vw: calc($unitless-value / $base * 100 * 1vw);

	@if ($unitless-value < 0) {
		@if ($min) {
			@return min($min, max($value-vw, $rem-value));
		} @else {
			@return max($value-vw, $rem-value);
		}
	} @else {
		@if ($min) {
			@return max($min, min($value-vw, $rem-value));
		} @else {
			@return min($value-vw, $rem-value);
		}
	}
}

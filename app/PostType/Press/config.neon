parameters:
	config:
		press:
			paging: 3 #temp low number - to testing

	postTypeRoutes:
		Press: press

cf:
	suggestUrls:
		searchContact:
			searchParameterName: search
			link: "/superadmin/search2/contact"
			params: []

cc:

application:
	mapping:
		Press: App\PostType\Press\*Module\Presenters\*Presenter

services:
	- App\PostType\Press\Model\Orm\PressLocalizationModel
	- App\PostType\Press\Model\PressLocalizationFacade
	- App\PostType\Press\AdminModule\Components\Form\PressFormPrescription(coreFormPath: %appDir%/PostType/Core/AdminModule/Components/Form)

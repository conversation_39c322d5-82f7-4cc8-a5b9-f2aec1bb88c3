<?php declare(strict_types = 1);

namespace App\Model\BucketFilter\SetupCreator\Catalog;

use App\Model\BucketFilter\ElasticItem\visibleInCatalog;
use App\Model\BucketFilter\ElasticItem\IsInPath;
use App\Model\BucketFilter\ElasticItem\IsPublic;
use App\Model\BucketFilter\SetupCreator\BasicElasticItemListGenerator;
use App\PostType\Page\Model\Orm\CatalogTree;

class BasicElasticItemList implements BasicElasticItemListGenerator
{

	private ?array $list;

	public function __construct(
		private readonly ?CatalogTree $pathObject = null
	)
	{
	}


	public function getBasicElasticItemList(): array
	{
		if (!isset($this->list)) {
			$baseFilterItems = [];
			if ($this->pathObject !== null) {
				$baseFilterItems[] = new IsInPath($this->pathObject);
			}

			$baseFilterItems[] = new IsPublic();
			$baseFilterItems[] = new visibleInCatalog();

			$this->list = $baseFilterItems;
		}

		return $this->list;
	}

}

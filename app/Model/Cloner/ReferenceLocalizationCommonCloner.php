<?php declare(strict_types = 1);

namespace App\Model\Cloner;

use App\Model\Orm\Mutation\Mutation;
use App\PostType\Reference\Model\Orm\ReferenceLocalization;
use App\PostType\Reference\Model\Orm\ReferenceRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use Nextras\Orm\Entity\IEntity;

class ReferenceLocalizationCommonCloner extends PostTypeCommonCloner implements CommonCloner
{

	public function __construct(
		private readonly ReferenceLocalizationRepository $referenceLocalizationRepository,
		private readonly ReferenceRepository $referenceRepository,
	)
	{
	}


	public function clone(IEntity $entity, Mutation $targetMutation, bool $forceHide = true): ReferenceLocalization
	{
		assert($entity instanceof ReferenceLocalization);
		$newReferenceLocalization = parent::clonePostType(
			$entity,
			$targetMutation,
			$this->referenceLocalizationRepository,
			$this->referenceRepository,
			'reference'
		);

		assert($newReferenceLocalization instanceof ReferenceLocalization);

		if ($forceHide) {
			$newReferenceLocalization->public = false;
		}

		$newReferenceLocalization->setRawValue('customFieldsJson', $entity->getRawValue('customFieldsJson'));
		$newReferenceLocalization->setRawValue('customContentJson', $entity->getRawValue('customContentJson'));
		$newReferenceLocalization->setAsModified('customFieldsJson');
		$newReferenceLocalization->setAsModified('customContentJson');



		return $newReferenceLocalization;
	}

}

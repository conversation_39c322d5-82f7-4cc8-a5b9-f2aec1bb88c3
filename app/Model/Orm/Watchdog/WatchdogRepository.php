<?php declare(strict_types = 1);

namespace App\Model\Orm\Watchdog;

use Nextras\Orm\Collection\ICollection;
use Nextras\Orm\Repository\Repository;

/**
 * @method Watchdog getById($id)
 * @method Watchdog[]|ICollection findBy(array $cond)
 * @method array findProductInstanceWatchedIds()
 *
 */
final class WatchdogRepository extends Repository
{

	public static function getEntityClassNames(): array
	{
		return [Watchdog::class];
	}

}

<?php declare(strict_types=1);

namespace App\Model\Orm\IpGeolocation;

use Nette\Utils\Json;
use Nextras\Orm\Entity\IEntity;
use Nextras\Orm\Repository\Repository;
use stdClass;

/**
 * @method IpGeolocation getById($id)
 */
final class IpGeolocationRepository extends Repository
{
	static function getEntityClassNames(): array
	{
		return [IpGeolocation::class];
	}

	public function getByIp(string $ip): IpGeolocation|IEntity|null
	{
		return $this->getBy([
			'ip' => $ip
		]);
	}

	public function getByIpLong(string $ip): IpGeolocation|null
	{
		return $this->getBy([
			'ipLong' => ip2long($ip)
		]);
	}

	public function create(string $ip, string $countryCode, stdClass $ipData): IpGeolocation
	{
		$ip2long = ip2long($ip);
		if ( ! is_int($ip2long)) {
			$ip2long = 0;
		}
		$ipGeolocation = new IpGeolocation();
		$ipGeolocation->ip = $ip;
		$ipGeolocation->ipLong = $ip2long;
		$ipGeolocation->countryCode = $countryCode;
		$ipGeolocation->data = new stdClass();


		$ipData = (array)$ipData;

		unset(
			$ipData['geoplugin_request'],
			$ipData['geoplugin_credit'],
		);

		$ipGeolocation->setRawValue('data', Json::encode($ipData));
		$ipGeolocation->setAsModified('data');

		$IpGeolocation = $this->persistAndFlush($ipGeolocation);
		assert($IpGeolocation instanceof IpGeolocation);

		return $IpGeolocation;
	}

}

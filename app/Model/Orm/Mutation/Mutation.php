<?php declare(strict_types = 1);

namespace App\Model\Orm\Mutation;

use App\Model\CustomField\LazyValue;
use App\Model\Orm\Alias\Alias;
use App\Model\Orm\AliasHistory\AliasHistory;
use App\Model\Orm\Document\Document;
use App\Model\Orm\EmailTemplate\EmailTemplate;
use App\Model\Orm\EsIndex\EsIndex;
use App\Model\Orm\MutationSetting\MutationSetting;
use App\Model\Orm\NewsletterEmail\NewsletterEmail;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalization;
use App\Model\Orm\ProductVariantPrice\ProductVariantPrice;
use App\Model\Pages;
use App\Model\PagesFactory;
use App\Model\Orm\State\State;
use App\Model\Orm\CurrencyContainer; // phpcs:ignore
use App\Model\Orm\JsonContainer; // phpcs:ignore
use App\PostType\Blog\Model\Orm\BlogLocalization;
use App\PostType\Collection\Model\Orm\CollectionLocalization;
use App\PostType\Page\Model\Orm\Tree;
use App\Model\Orm\User\User;
use App\Model\Orm\UserMutation\UserMutation;
use Brick\Money\Currency;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\ManyHasOne;
use Nextras\Orm\Relationships\OneHasMany;
use App\Model\Orm\Traits\HasConfigService;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasFormDefaultData;

/**
 * @property int $id {primary}
 * @property int $public {default 1}
 * @property int $cmsOrder {default 0}
 * @property string $langCode {enum self::CODE_*}
 * @property string|null $name
 * @property string|null $langMenu {enum self::ISO_CODE_PREFIX_*}
 * @property string $isoCodePrefix
 * @property string $pimpCode
 *
 * @property string|null $adminEmail
 * @property string|null $contactEmail
 * @property string|null $hrEmail
 * @property string|null $orderEmail
 * @property string|null $fromEmail
 * @property string|null $fromEmailName
 * @property Currency $currency {container CurrencyContainer}
 * @property ArrayHash $synonyms {container JsonContainer}
 * @property string|null $heurekaOverenoKey
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property bool $hidePrices {default false}
 *
 * RELATIONS
 * @property OneHasMany|MutationSetting[] $mutationSettings  {1:m MutationSetting::$mutation}
 * @property OneHasMany|EmailTemplate[] $emailTemplates  {1:m EmailTemplate::$mutation}
 * @property OneHasMany|EsIndex[] $esIndexes  {1:m EsIndex::$mutation}
 * @property ProductLocalization[]|OneHasMany $productLocalizations {1:m ProductLocalization::$mutation}
 * @property ProductVariantLocalization[]|OneHasMany $variantLocalizations {1:m ProductVariantLocalization::$mutation}
 * @property OneHasMany|Alias[] $aliases  {1:m Alias::$mutation}
 * @property OneHasMany|Tree[] $trees {1:m Tree::$mutation}
 * @property OneHasMany|AliasHistory[] $historyAliases  {1:m AliasHistory::$mutation}
 *
 * @property ManyHasMany|User[]|null $users {m:m User::$mutations}
 *
 * @property OneHasMany|ProductVariantPrice[] $productVariantPrices  {1:m ProductVariantPrice::$mutation}
 * @property State[]|ManyHasMany $states {m:m State::$mutations, isMain=true}
 * @property UserMutation[]|OneHasMany $userMutations {1:m UserMutation::$mutation}
 * @property NewsletterEmail[]|OneHasMany $newsletterEmails {1:m NewsletterEmail::$mutation}
 * @property Document[]|OneHasMany $documents {1:m Document::$mutation}
 *
 * @property BlogLocalization[]|OneHasMany $blogLocalizations {1:m BlogLocalization::$mutation}
 * @property CollectionLocalization[]|OneHasMany $collectionLocalizations {1:m CollectionLocalization::$mutation}
 *
 * VIRTUAL
 * @property-read Pages $pages {virtual}
 * @property-read Tree $rootPage {virtual}
 * @property-read string|null $domain {virtual}
 * @property-read string|null $urlPrefix {virtual}
 * @property-read string|null $region {virtual} https://app.sistrix.com/en/hreflang-generator
 * @property-read int $rootId {virtual}
 * @property-read int|null $hidePageId {virtual}
 * @property-read bool $isDefault {virtual}
 * @property-read bool $isInternational {virtual}
 * @property ArrayHash|null $cf {virtual}
 * @property-read array $paymentsArray {virtual}
 * @property-read ArrayHash $cookie {virtual}
 * @property-read bool $isEnabledCookieModal {virtual}
 */
class Mutation extends Entity
{

	use HasConfigService;
	use HasCustomFields;
	use HasFormDefaultData;
	use HasConsts;

	public const CODE_CS = 'cs';
	public const CODE_EN = 'en';
	public const CODE_SK = 'sk';
	public const CODE_DE = 'de';
	public const CODE_PL = 'pl';
	public const CODE_ES = 'es';
	public const CODE_Uk = 'uk';
	public const CODE_FR = 'fr';

	public const CODE_IT = 'it';
	public const CODE_XX = 'xx';

	public const ISO_CODE_PREFIX_CS = 'cs';
	public const ISO_CODE_PREFIX_EN = 'en';

	public const SYNONYMS_DELIMITER = ',';

	public const DEFAULT_CODE = self::CODE_CS; // urceni defaultni mutace
	public const DEFAULT_RS_CODE = self::CODE_CS;

	private Pages $pagesObject;

	public function injectPages(PagesFactory $pagesFactory): void
	{
		$this->pagesObject = $pagesFactory->create($this);
	}


	public function getRealDomain(): string
	{
		// todo remove

		return $this->domain;
	}


	public function getRealDomainWithoutWWW(): string
	{
		return str_replace('www.', '', $this->domain);
	}


	public function getRealRootId(): int
	{
		$config = $this->configService->get('mutations', $this->langCode, 'rootId');
		if ($config) {
			$realRootId = $config;
		} else {
			$realRootId = $this->rootId;
		}

		return (int) $realRootId;
	}

	public function getRealUrlPrefix(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'urlPrefix');
		if ($config) {
			$realUrlPrefix = $config;
		} else {
			$realUrlPrefix = $this->urlPrefix;
		}

		if (is_string($realUrlPrefix)) {
			return str_replace('/', '', $realUrlPrefix);
		} else {
			return '';
		}
	}

	public function getGTMCode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('mutations', $this->langCode);
		return $domain['gtmCode'] ?? '';
	}

	public function getGACode(): string
	{
		// TODO zvazit rozsireni: zadavani kodu pres administraci...
		$domain = $this->configService->getParam('mutations', $this->langCode);
		return $domain['googleAnalyticsCode'] ?? '';
	}


	/**
	 * @param string $key .. values = admin|contact|order|breeding
	 * @return bool|mixed
	 */
	public function getEmail(string $key = 'admin')
	{
		$col = $key . 'Email';
		if (!isset($this->$col)) {
			return false;
		}

		$mutation = $this->configService->getParam('mutations', $this->langCode);

		if ($mutation && isset($mutation[$col]) && $mutation[$col]) {
			return $mutation[$col];
		} else {
			return $this->$col;
		}
	}


	public function getRealFromEmail(): string
	{
		$config = $this->configService->get('mutations', $this->langCode, 'fromEmail');
		if ($config) {
			$realEmailFrom = $config;
		} else {
			$realEmailFrom = $this->fromEmail;
		}

		return $realEmailFrom;
	}



	public function getBaseUrl(): string
	{
		return 'https://' . $this->domain;
	}


	public function getBaseUrlWithPrefix(): string
	{
		if ((bool) $this->urlPrefix) {
			return 'https://' . $this->domain . '/' . $this->urlPrefix;
		} else {
			return 'https://' . $this->domain;
		}
	}


	public function getUrl(): string
	{
		return $this->getBaseUrlWithPrefix();
	}


	protected function getterPages(): Pages
	{
		return $this->pagesObject;
	}

	protected function getterRootPage(): ?Tree
	{
		return $this->trees->toCollection()->getById($this->getRealRootId());
	}

	protected function getterHidePageId(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'hidePageId');
	}


	protected function getterRootId(): int
	{
		return (int)$this->configService->get('mutations', $this->langCode, 'rootId');
	}

	protected function getterRegion(): mixed
	{
		return !empty($this->cf->mutationData->region) ? $this->cf->mutationData->region : $this->langCode;
	}


	protected function getterUrlPrefix(): mixed
	{
		return $this->configService->get('mutations', $this->langCode, 'urlPrefix');
	}


	protected function getterDomain(): string
	{
		$domainFromConfig = $this->configService->get('mutations', $this->langCode, 'domain');
		if ($domainFromConfig === null) {
			throw new \LogicException(sprintf('Missing domain for \'%s\' mutation in neon', $this->langCode));
		}

		return (string) $domainFromConfig;
	}


	protected function getterIsDefault(): bool
	{
		return $this->langCode === self::DEFAULT_CODE;
	}

	/** atyp balistas.com */
	protected function getterIsInternational(): bool
	{
		return $this->langCode === self::CODE_EN;
	}

	protected function getterCookie(): ArrayHash
	{
		$cookieSetup = $this->configService->get('mutations', $this->langCode, 'cookie') ?? [];
		return ArrayHash::from($cookieSetup);
	}

	protected function getterIsEnabledCookieModal(): bool
	{
		return $this->configService->get('cookie', 'enableModal') && isset($this->cookie->enableModal) && $this->cookie->enableModal;
	}


	public function getLangSwitcherCode(): string
	{
		return match($this->langCode) {
			self::CODE_CS => 'cz',
			default => $this->langCode,
		};
	}

	public function getDefaultState(): ?State
	{
		if (isset($this->cf->mutationData->defaultState)
			&& $this->cf->mutationData->defaultState instanceof LazyValue) {
			$state = $this->cf->mutationData->defaultState->getEntity();
			if ($state instanceof State) {
				return null;
			}
		}

		return null;

	}

	public function getPriceListName(): string
	{
		return match($this->langCode) {
			self::CODE_SK => 'SK_s_DPH',
			default => 'CZ_s_DPH',
		};
	}

	public function showPrice(): bool
	{
		return !$this->hidePrices;
	}

	/**
	 * is mutation set for production?
	 * it depends on settings "robots" => 'index, follow'
	 *
	 * @return bool
	 */
	public function isInProduction(): bool
	{
		$lang = $this->configService->getParam('mutations', $this->langCode);
		return (!isset($lang["robots"]) || str_starts_with($lang["robots"], 'index'));
	}

}

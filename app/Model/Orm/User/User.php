<?php declare(strict_types = 1);

namespace App\Model\Orm\User;

use App\Model\Orm\ConfigurationOrder\ConfigurationOrder;
use App\Model\Orm\JsonContainer;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\ProductReview\ProductReview;
use App\Model\Orm\State\State;
use App\Model\Orm\Traits\HasConfigService;
use App\Model\Orm\Traits\HasConsts;
use App\Model\Orm\Traits\HasCustomFields;
use App\Model\Orm\Traits\HasTranslator;
use App\Model\Orm\UserHash\UserHash;
use App\Model\Orm\UserMutation\UserMutation;
use Nette\Utils\ArrayHash;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\ManyHasMany;
use Nextras\Orm\Relationships\OneHasMany;
use function json_decode;
use function json_encode;
use const JSON_THROW_ON_ERROR;

/**
 * @property int $id {primary}
 * @property string $email
 * @property string|null $password
 * @property string|null $googleId
 * @property string|null $status {default self::STATUS_APPROVED} {enum self::STATUS_*}
 * @property string|null $role {default self::ROLE_USER} {enum self::ROLE_*}
 * @property string|null $firstname {default ''}
 * @property string|null $lastname {default ''}
 * @property string|null $phone {default ''}
 * @property string $street {default ''}
 * @property string $zip {default ''}
 * @property string $city {default ''}
 * @property string $ic {default ''}
 * @property string $dic {default ''}
 * @property bool $projectReservation {default false}
 * @property string $company {default ''}
 * @property string $region {default ''}
 * @property DateTimeImmutable|null $createdTime {default now}
 * @property DateTimeImmutable|null $editedTime
 * @property DateTimeImmutable|null $lastLogin
 * @property string|null $customAddressJson
 * @property int $orderCount {default 0}
 * @property ArrayHash $customFieldsJson {container JsonContainer}
 * @property string|null $hash
 *
 *
 * RELATIONS
 * @property ProductReview[]|OneHasMany $reviews {1:m ProductReview::$user}
 * @property UserHash[]|OneHasMany $hashes {1:m UserHash::$user}
 * @property Mutation[]|ManyHasMany $mutations {m:m Mutation::$users, isMain=true}
 * @property PriceLevel $priceLevel {m:1 PriceLevel::$users}
 * @property State|null $state {m:1 State::$users}
 * @property UserMutation[]|OneHasMany $userMutations {1:m UserMutation::$user, cascade=[persist, remove]}
 * @property ConfigurationOrder[]|OneHasMany $configurationOrders {1:m ConfigurationOrder::$user}
 *
 * VIRTUAL
 * @property-read string $name {virtual}
 * @property-read Mutation|null $mutation {virtual}
 * @property array|null $customAddress {virtual}
 * @property ArrayHash|null $cf {virtual}
 */
class User extends Entity
{

	use HasConsts;
	use HasConfigService;
	use HasTranslator;
	use HasCustomFields;

	public const ROLE_ARCHITECT = 'architect';
	public const ROLE_DEALER = 'dealer';
	public const ROLE_JOURNALIST = 'journalist';
	public const ROLE_USER = 'user';
	public const ROLE_TON_EMPLOYEE = 'tonEmployee';

	public const STATUS_NEW = 'new';
	public const STATUS_WAITING = 'waiting';
	public const STATUS_APPROVED = 'approved';
	public const STATUS_DISABLED = 'disabled';


	public const B2B_ROLES = [
		self::ROLE_USER => self::ROLE_USER,
		self::ROLE_TON_EMPLOYEE => self::ROLE_TON_EMPLOYEE,
		self::ROLE_DEALER => self::ROLE_DEALER,
		self::ROLE_ARCHITECT => self::ROLE_ARCHITECT,
		self::ROLE_JOURNALIST => self::ROLE_JOURNALIST,

	];

	public function onCreate(): void
	{
		$this->hash = Random::generate(20) . time();
	}

	public function getRegionName(): string
	{
		if ($this->state === null) {
			return '';
		}
		return match($this->state->code) {
			default => '',
			State::DEFAULT_CODE_SK => isset(State::REGIONS_SK[$this->region]) ? State::REGIONS_SK[$this->region] : '',
			State::DEFAULT_CODE => isset(State::REGIONS_CZ[$this->region]) ? State::REGIONS_CZ[$this->region] : '',
		};
	}

	protected function getterCustomAddress(): mixed
	{
		assert($this->getMetadata()->hasProperty('customAddressJson'));
		if ($this->customAddressJson === null) {
			return null;
		}

		return json_decode($this->customAddressJson, flags: JSON_THROW_ON_ERROR);
	}


	protected function setterCustomAddress(mixed $customAddress): void
	{
		assert($this->getMetadata()->hasProperty('customAddressJson'));
		$this->customAddressJson = json_encode($customAddress, JSON_THROW_ON_ERROR);
	}

	protected function getterMutation(): ?Mutation
	{
		return $this->mutations->toCollection()->fetch();
	}

	protected function getterName(): string
	{
		return trim($this->firstname . ' ' . $this->lastname);
	}

	public function hasPriceList(): bool
	{
		return isset($this->state->priceList) && $this->state->priceList !== '';
	}

}

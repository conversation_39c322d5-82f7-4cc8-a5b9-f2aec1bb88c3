{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : 'md'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '3xl'}

{default $class = false}
{default $highlighted = false}
{default $image = false}
{default $video = false}
{default $author = false}
{default $position = false}
{default $btn = false}
{default $title = false}
{default $quote = false}
{default $content = false}
{default $sizes = ['100vw', '50vw', '69.6rem']}
{default $vertical = false}
{default $ratio = '5-3'}
{default $holderClass = false}
{default $btnClass = false}
{default $contentClass = 'u-maw-6-12'}
{default $quoteClass = 'h1'}
{default $isWideIllust = false}
{default $isReversed = false}

<figure n:if="$quote || $content" n:class="b-quote, $vertical ? b-quote--vertical, $highlighted ? b-quote--highlighted,$isReversed ? 'b-quote--reversed', $class, $spacingBottom ? 'u-mb-' . $spacingBottom, $spacingBottomLg ? 'u-mb-' . $spacingBottomLg . '@md'">
	{capture $titleBlock}
		<h2 n:if="$title" class="b-quote__title h1 u-maw-5-12">
			{$title}
		</h2>
	{/capture}

	{capture $quoteBlock}
		<figure n:ifcontent n:class="b-quote__holder, u-mb-last-0, $holderClass">
			<figcaption n:ifcontent class="b-quote__author">
				<cite n:if="$author" class="b-quote__name">{$author|noescape}</cite>
				{if $position}{$position|noescape}{/if}
			</figcaption>
			<blockquote n:if="$quote" n:class="b-quote__quote, $quoteClass">
				{$quote|texy|noescape}
			</blockquote>
		</figure>
	{/capture}

	{capture $contentBlock}
		<div n:if="$content" n:class="b-quote__content, $contentClass, u-mb-last-0">
			{$content|noescape}
		</div>
	{/capture}

	{capture $btnBlock}
		<p n:if="$btn" n:class="b-quote__btn, $btnClass">
			{if isset($btn->page) && $btn->page instanceOf App\Model\CustomField\LazyValue && $btn->page->getEntity()}

				<a href="{plink $btn->page}" class="btn btn--secondary btn--transparent">
					<span class="btn__text">
						<span n:tag-if="!$content && !$title" class="item-icon">
							{if !$content && !$title}
								{('arrow-right')|icon, 'item-icon__icon'}
							{/if}
							<span n:tag-if="!$content && !$title" class="item-icon__text">
								{if isset($btn->text)}
									{$btn->text}
								{else}
									{$btn->page->nameAnchor}
								{/if}
							</span>
						</span>
					</span>
				</a>
			{elseif isset($btn->external) && isset($btn->text)}
				<a href="{$btn->external}" class="btn btn--secondary btn--transparent" target="_blank" rel="noopener noreferrer">
					<span class="btn__text">
						<span n:tag-if="!$content && !$title" class="item-icon">
							{if !$content && !$title}
								{('arrow-right')|icon, 'item-icon__icon'}
							{/if}
							<span n:tag-if="!$content && !$title" class="item-icon__text">
								{$btn->text}
							</span>
						</span>
					</span>
				</a>
			{/if}
		</p>
	{/capture}

	{if $image || $video}
		<div n:class="b-quote__grid, grid, $vertical && $highlighted ? grid--y-sm">
			<div n:class="grid__cell, (!$vertical && $ratio != '5-7' && !$isWideIllust) ? 'size--6-12@md', (!$vertical && $content && !$isWideIllust) ? 'size--5-12@md', !$vertical && $isWideIllust ? 'size--8-12@md'">
				{if $video}
					{include $templates.'/part/core/video.latte', class=>'b-quote__video', poster=>$image ? $image->getSize('lg')->src, ratio=>$ratio, link=>$video, title=>false}
				{elseif $image}
					<p class="b-quote__img img img--{$ratio} u-mb-0">
						{php $imgSm = $image->getSize('sm')}
						{php $imgMd = $image->getSize('md')}
						{php $imgLg = $image->getSize('lg')}
						{php $imgXl = $image->getSize('xl')}

						<img src="{$imgXl->src}"
							srcset="
								{$imgSm->src} 460w,
								{$imgMd->src} 700w,
								{$imgLg->src} 1060w,
								{$imgXl->src} 1420w"
							sizes="
								(max-width: 46.875rem) {$sizes[0]},
								(max-width: 91.5rem) {$sizes[1]},
								{$sizes[2]}"
							alt="" loading="lazy">
					</p>
				{/if}
			</div>
			<div n:class="grid__cell, (!$vertical && $ratio != '5-7' && !$isWideIllust) ? 'size--6-12@md', (!$vertical && $content && !$isWideIllust) ? 'size--7-12@md', !$vertical && $isWideIllust ? 'size--4-12@md'">
				{$titleBlock}
				{$quoteBlock}
				{$contentBlock}
				{$btnBlock}
			</div>
		</div>
	{else}
		{$titleBlock}
		{$quoteBlock}
		{$contentBlock}
		{$btnBlock}
	{/if}

</figure>

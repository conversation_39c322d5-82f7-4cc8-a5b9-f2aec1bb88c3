{default $spacingBottom = isset($spacing->mobile) ? $spacing->mobile : 'md'}
{default $spacingBottomLg = isset($spacing->desktop) ? $spacing->desktop : '3xl'}
{default $class = false}
{default $images = []}
{default $horizontal = false}

<div n:if="count($images)" n:class="b-images, $class, $spacingBottom ? 'u-mb-' . $spacingBottom, $spacingBottomLg ? 'u-mb-' . $spacingBottomLg . '@md'">
	<div class="b-images__carousel embla" data-controller="embla">
		<div class="embla__viewport" data-embla-target="viewport">
			<ul class="b-images__grid grid grid--scroll grid--y-0 embla__container">
				<li n:foreach="$images as $image" n:class="b-images__cell, grid__cell, 'size--6-12@sm size--4-12@md', !$horizontal ? 'size--3-12@xl'">
					{php $imgSm = $image->getSize('md')}
					{php $imgXl = $image->getSize('xl')}
					<a href="{$imgXl->src}" n:class="b-images__img, img, $horizontal ? img--3-2 : img--5-7, img--contain" data-modal='{"gallery": "images"}'>
						<img src="{$imgSm->src}" alt="{$image->name}" loading="lazy">
					</a>
				</li>
			</ul>
		</div>
		<button class="embla__btn embla__btn--prev" disabled="disabled" type="button" data-action="embla#prev" data-embla-target="prevButton">
			{('arrow-left')|icon}
			<span class="u-vhide">{_btn_prev}</span>
		</button>
		<button class="embla__btn embla__btn--next" disabled="disabled" type="button" data-action="embla#next" data-embla-target="nextButton">
			{('arrow-right')|icon}
			<span class="u-vhide">{_btn_next}</span>
		</button>
	</div>
</div>

{block content}
	{snippet content}
		{*THICKBOX*}
		{if $presenter->isAjax()}
			{include $templates.'/part/box/content.latte'}
		{else}
			<div class="row-main">
				<div class="u-mb-last-0 u-pt-md u-mb-md u-mb-3xl@md">
					{include $templates.'/part/box/annot.latte', class=>'b-annot--bz u-mb-md u-mb-xl@lg'}
					{include $templates.'/part/crossroad/std.latte'}
					{include $templates.'/part/box/content.latte'}

					{embed $templates.'/part/form/std.latte', spacingBottomLg=>'xl',
						title: $translator->translate('form_title_reservation'),
						content: $translator->translate('form_content_reservation')
					}
						{block form}
							{control projectForm}
						{/block}
					{/embed}

					<hr class="u-mb-md u-mb-2xl@md">

					{control customContentRenderer}
				</div>
			</div>
		{/if}
	{/snippet}
{/block}

<?php declare(strict_types = 1);

namespace App\FrontModule\Components\ProductParameters;

use App\Model\BucketFilter\CatalogParameter;
use App\Model\Orm\Orm;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\TranslatorDB;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nextras\Orm\Collection\DbalCollection;
use Nextras\Orm\Collection\ICollection;
use stdClass;

/**
 * @property-read DefaultTemplate $template
 */
class ProductParameters extends UI\Control
{

	public function __construct(
		private readonly Product $product,
		private readonly ProductVariant $variant,
		private readonly Orm $orm,
		private readonly TranslatorDB $translator,
		private readonly CatalogParameter $catalogParameter,
	)
	{
	}


	public function render(mixed $param = null): void
	{
		$this->template->setTranslator($this->translator);
		if ($mainCategory = $this->product->mainCategory) {
			$mainPageParameters = $this->catalogParameter->getParametersCfForFilter($mainCategory);
			$this->template->mainPageParametes = $mainPageParameters;
		}

//		$productParameters = $this->orm->parameter->findParametersForProduct($this->product, $uids);
		$filterableUids = [];

		$this->template->product = $this->product;
		$this->template->mainCategory = $this->product->mainCategory;
		$this->template->filterableUids = $filterableUids;

		$this->template->param = $param;

		$uids = $param["paramsList"] ?? [];
		$listType = $param["listType"] ?? "productDetail";

		if ($listType === "productInstanceParams") {
			$template = "productParameterChecked.latte";
		} else {
			$template = "productParameters.latte";
		}

		$this->template->variantParameters = $this->getVariantParameters($uids);

		$this->template->render(__DIR__ . '/' . $template);
	}

	private function getVariantParameters(array $uids = []): array
	{

		if (count($uids) === 0) {
			$uids = [
				Parameter::UID_WIDTH,
				Parameter::UID_DEPTH,
				Parameter::UID_HEIGHT,
				Parameter::UID_VYSKA_SED,
				Parameter::UID_SIRKA_SED,
				Parameter::UID_HLOUB_SED,
				Parameter::UID_WEIGHT,
				Parameter::UID_CONSUMPTION_OF_FABRIC,
				Parameter::UID_LEATHER_CONSUMPTION,
				Parameter::UID_OBJEM_KAR1,
				Parameter::UID_OBJEM_KAR2,
				Parameter::UID_OBJEM_KAR3,
				Parameter::UID_ROZM_KAR1,
				Parameter::UID_ROZM_KAR2,
				Parameter::UID_ROZM_KAR3,
				Parameter::UID_STACK_COUNT,
				Parameter::UID_LINE_CONNECTION,
				Parameter::UID_WOOD,
			];
		}

		$variantParametersValuesIds = [];
		if (isset($this->variant->parameters)) {
			foreach ($this->variant->parameters as $key=>$variantParameterValueIds) {
				if (!in_array($key, ['priceData'])) {
					$variantParametersValuesIds = array_merge($variantParametersValuesIds, (array)$variantParameterValueIds);
				}
			}
		}
		$structuredParameters = [];
		$parameterValues = $this->orm->parameterValue->findBy([
			'id' => $variantParametersValuesIds,
			'parameter->uid' => $uids,
		])->orderBy('parameter->sort');

		foreach ($parameterValues as $parameterValue) {
			if (!isset($structuredParameters[$parameterValue->parameter->uid])) {
				$structuredParameters[$parameterValue->parameter->uid] = new stdClass();
				$structuredParameters[$parameterValue->parameter->uid]->values = [];
			}
			$structuredParameters[$parameterValue->parameter->uid]->parameter = $parameterValue->parameter;
			$structuredParameters[$parameterValue->parameter->uid]->values[] = $parameterValue;
		}


		foreach ($structuredParameters as $structuredParameter) {
			$parameter = $structuredParameter->parameter;
			if ($parameter->type === Parameter::TYPE_NUMBER) {
				usort($structuredParameter->values, function ($a, $b) {
					return (int)((float)$a->internalValue > (float)$b->internalValue);
				});
			} /*else {
				usort($structuredParameter->values, function ($a, $b) {
					return strcoll($a->value, $b->value);
				});
			}*/
		}


		$structuredParameters = $this->processVolumeParameters($structuredParameters);
		$structuredParameters = $this->processWeightParameter($structuredParameters, Parameter::UID_WEIGHT);
		$structuredParameters = $this->processSizeParameter($structuredParameters, Parameter::UID_ROZM_KAR1);
		$structuredParameters = $this->processSizeParameter($structuredParameters, Parameter::UID_ROZM_KAR2);
		$structuredParameters = $this->processSizeParameter($structuredParameters, Parameter::UID_ROZM_KAR3);

		return $structuredParameters;
	}


	// bereme az 3 prametry objemu, z kazdeho najdeme nejmensi a nejvetsi hodnotu a sečteme mezi jednotlivými parametry
	private function processVolumeParameters(array $parameters): array
	{
		if (!isset($parameters[Parameter::UID_OBJEM_KAR1]) && !isset($parameters[Parameter::UID_OBJEM_KAR2]) && !isset($parameters[Parameter::UID_OBJEM_KAR3])) {
			return $parameters;
		}
		$minAll = 0;
		$maxAll = 0;
		if (isset($parameters[Parameter::UID_OBJEM_KAR1])) {
			$allItems = [];
			foreach ($parameters[Parameter::UID_OBJEM_KAR1]->values as $v) {
				$allItems[] = $v->value;
			}
			$minAll += min($allItems);
			$maxAll += max($allItems);
		}
		if (isset($parameters[Parameter::UID_OBJEM_KAR2])) {
			$allItems = [];
			foreach ($parameters[Parameter::UID_OBJEM_KAR2]->values as $v) {
				$allItems[] = $v->value;
			}
			$minAll += min($allItems);
			$maxAll += max($allItems);
		}
		if (isset($parameters[Parameter::UID_OBJEM_KAR3])) {
			$allItems = [];
			foreach ($parameters[Parameter::UID_OBJEM_KAR3]->values as $v) {
				$allItems[] = $v->value;
			}
			$minAll += min($allItems);
			$maxAll += max($allItems);
		}

		if (isset($parameters[Parameter::UID_OBJEM_KAR2])) {
			unset($parameters[Parameter::UID_OBJEM_KAR2]);
		}
		if (isset($parameters[Parameter::UID_OBJEM_KAR3])) {
			unset($parameters[Parameter::UID_OBJEM_KAR3]);
		}

		$newParamValue = new stdClass();
		if ($minAll <> $maxAll) {
			$newParamValue->value = $minAll." ".$this->translator->translate("to2")." ".$maxAll;
		} else {
			$newParamValue->value = (string) $minAll;
		}
		$parameters[Parameter::UID_OBJEM_KAR1]->values = [$newParamValue];

		return $parameters;
	}


	private function processSizeParameter(array $parameters, string $key): array
	{
		if (!isset($parameters[$key])) {
			return $parameters;
		}

		$allItems = [];
		foreach ($parameters[$key]->values as $v) {
			$allItems[$v->value] = $v->value;
		}

		// "69 x 69 x 8" => jednotlivá čísla -> objem
		foreach ($allItems as $k=>$v) {
			$tmp = explode("x", $v);
			$allItems[$k] = (double) $tmp[0] * (double) $tmp[1] * (double) $tmp[2];
		}
		$max = max($allItems);
		$min = min($allItems);
		$maxValue = array_keys($allItems, $max);
		$minValue = array_keys($allItems, $min);

		$newParamValue = new stdClass();
		if ($min <> $max) {
			$newParamValue->value = $minValue[0]." ".$this->translator->translate("to2")." ".$maxValue[0];
		} else {
			$newParamValue->value = (string) $minValue[0];
		}
		$parameters[$key]->values = [$newParamValue];

		return $parameters;
	}


	private function processWeightParameter(array $parameters, string $key): array
	{
		// hmotnost - nejnizsi a nejvyssi
		if (!isset($parameters[$key])) {
			return $parameters;
		}

		$allItems = [];
		foreach ($parameters[$key]->values as $v) {
			$allItems[$v->value] = $v->value;
		}
		$min = min($allItems);
		$max = max($allItems);

		$newParamValue = new stdClass();
		if ($min <> $max) {
			$newParamValue->value = $min." ".$this->translator->translate("to2")." ".$max;
		} else {
			$newParamValue->value = (string) $min;
		}
		$parameters[$key]->values = [$newParamValue];

		return $parameters;
	}

}

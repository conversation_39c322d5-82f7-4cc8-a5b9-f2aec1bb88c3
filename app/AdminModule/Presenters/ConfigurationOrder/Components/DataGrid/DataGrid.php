<?php declare(strict_types = 1);

namespace App\AdminModule\Presenters\ConfigurationOrder\Components\DataGrid;

use App\Model\Link\LinkFactory;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrder;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrderModel;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrderRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\State\StateRepository;
use App\Model\Translator;
use App\PostType\Core\AdminModule\Components\DataGrid\HasMutationColumn;
use App\PostType\ProductInstance\Model\Orm\ProductInstance;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;
use GuzzleHttp\Tests\Stream\Str;
use Nette\Application\UI\Control;
use Nette\Utils\Html;
use Nette\Utils\Strings;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Ublaboo\DataGrid\Column\Action\Confirmation\StringConfirmation;
use Ublaboo\DataGrid\Column\ColumnDateTime;
use Ublaboo\DataGrid\Column\ColumnText;

class DataGrid extends Control
{

	use HasMutationColumn;


	public function __construct(
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly ConfigurationOrderRepository $configurationOrderRepository,
		private readonly ConfigurationOrderModel $configurationOrderModel,
		private readonly LinkFactory $linkFactory,
		private readonly StateRepository $stateRepository,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;
		$template->setTranslator($this->translator);

		$template->render(__DIR__ . '/dataGrid.latte');
	}

	public function createComponentGrid(): \Ublaboo\DataGrid\DataGrid
	{
		$grid = new \Ublaboo\DataGrid\DataGrid();
		$grid->setDataSource($this->configurationOrderRepository->findAll());
		$grid->setTranslator($this->translator);


		$grid->addColumnText('state', 'state', 'state')
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
				return $configurationOrder->state->name;
			})->setFilterSelect($this->stateRepository->findAll()->orderBy('name')->fetchPairs('id', 'name'))->setPrompt($this->translator->translate('all'));

		$grid->addColumnText('contact', 'contact', 'contact')
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
			return $configurationOrder->contact?->internalName ?? '';
		})->setFilterSelect($this->orm->contact->findAll()->fetchPairs('id', 'internalName'))->setPrompt($this->translator->translate('all'));

		$grid->addColumnText('name', 'name', 'name')
			->setFilterText()->setSplitWordsSearch(false);

		$grid->addColumnText('email', 'email', 'email')
			->setFilterText()->setSplitWordsSearch(false);

		$grid->addColumnText('phone', 'phone', 'phone')
			->setFilterText()->setSplitWordsSearch(false);

		$grid->addColumnText('variant', 'variant', 'variant')
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
				$parameters = $this->getConfigurationLinkParameters($configurationOrder);
				$parameters['mutation'] = $configurationOrder->mutation;
				$div = Html::el('div');
				foreach ($configurationOrder->items as $item) {
					$itemHtml = Html::el('a')->href($this->linkFactory->linkTranslateToNette(
						$item->variant->product->getLocalization($configurationOrder->mutation), $parameters, 'configurator')
					)
						->addAttributes([
							'target' => '_blank',
							'class' => 'btn btn-xs btn-secondary',
						])
						->setText($item->variant->code);
					$div->addHtml($itemHtml);
				}
				return $div;
			})
		;
//		$grid->addColumnText('productInstance', 'productInstance', 'productInstance')
//			->setRenderer(function (ConfigurationOrder $configurationOrder) {
//				if ($configurationOrder->productInstance !== null) {
//					return 'Ano';
//				} else {
//					return 'Ne';
//				}
//			});
		$grid->addColumnText('count', 'count', 'count');
		$grid->addColumnText('code', 'code', 'code')
			->setFilterText()->setSplitWordsSearch(false);
		$grid->addColumnText('text', 'text', 'text')
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
				if (Strings::length($configurationOrder->text) > 30) {
					return Strings::substring($configurationOrder->text , 0, 30) . ' ...';
				} else {
					return $configurationOrder->text;
				}
			})
			->setFilterText()->setSplitWordsSearch(false);

		$grid->setItemsDetail();

		$grid->setTemplateFile(__DIR__ . '/item-detail-grid.latte');
		$this->addColumnMutation($grid);
		$grid->addColumnDateTime('createdAt', 'createdAt', 'createdAt')->setSortable();
//		$grid->addColumnDateTime('noticeSentAt', 'noticeSentAt', 'noticeSentAt')->setSortable();
//		$grid->addColumnDateTime('inquirySentAt', 'inquirySentAt', 'inquirySentAt')->setSortable();
		$grid->addColumnDateTime('offerSentAt', 'offerSentAt', 'offerSentAt')->setSortable();

//		$grid->addAction('inquirySend', 'inquirySend', 'inquirySend!')
////			->setIcon('trash')
//			->setClass('btn btn-xs btn-primary x-ajax')
//			->setConfirmation(
//				new StringConfirmation('Pravdu chcete tuto zprávu odeslat?') // Second parameter is optional
//			)->setRenderCondition(fn (ConfigurationOrder $configurationOrder) => $configurationOrder->inquirySentAt === null);

		//custom collumns for export
		$colState =  (new ColumnText($grid, 'state', 'state', 'state'))
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
				return $configurationOrder->state->name;
		});
		$colContact =  (new ColumnText($grid, 'contact', 'contact', 'contact'))
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
				return $configurationOrder->contact?->internalName ?? '';
			});
		$colName = new ColumnText($grid, 'name', 'name', 'name');
		$colText = new ColumnText($grid, 'text', 'text', 'text');
		$colEmail = new ColumnText($grid, 'email', 'email', 'email');
		$colPhone = new ColumnText($grid, 'phone', 'phone', 'phone');
		$colVariant =  (new ColumnText($grid, 'variant', 'variant', 'variant'))
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
					return $configurationOrder->variant->code;
		});
		$colVariantURL =  (new ColumnText($grid, 'variantURL', 'variantURL', 'variant'))
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
				$parameters = $this->getConfigurationLinkParameters($configurationOrder);
				$parameters['mutation'] = $configurationOrder->mutation;

				return $this->linkFactory->linkTranslateToNette(
					$configurationOrder->variant->product->getLocalization($configurationOrder->mutation), $parameters, 'configurator');
		});
		$productInstance =  (new ColumnText($grid, 'productInstance', 'productInstance', 'productInstance'))
			->setRenderer(function (ConfigurationOrder $configurationOrder) {
				if ($configurationOrder->productInstance !== null) {
					return 'Ano';
				} else {
					return 'Ne';
				}
			});
		$colCount = new ColumnText($grid, 'count', 'count', 'count');
		$colCode = new ColumnText($grid, 'code', 'code', 'code');
		$colCreatedAt = new ColumnDateTime($grid, 'createdAt', 'createdAt', 'createdAt');
		$offerSentAt = new ColumnDateTime($grid, 'offerSentAt', 'offerSentAt', 'offerSentAt');

		$colsForExport = [$colState, $colContact, $colName, $colEmail, $colPhone, $colVariant, $productInstance, $colCount, $colCode, $colCreatedAt, $offerSentAt, $colVariantURL, $colText];

		$grid->addExportCsv('Csv export (filtered)', 'inquiries.csv', 'UTF-8', ';', false, true)->setColumns($colsForExport);
		$grid->addExportCsv('Csv export (filtered) for windows', 'inquiries.csv', 'windows-1250', ';', false, true)->setColumns($colsForExport);

		$grid->setDefaultSort(['createdAt' => 'DESC']);
		return $grid;
	}

	public function handleInquirySend(int $id): void
	{
		$this->configurationOrderModel->sendInquiry(
			$this->configurationOrderRepository->getByIdChecked($id)
		);
		$this->redirect('this');
	}

	public function handleAnsweredByDealer(int $id): void
	{
		$this->configurationOrderModel->setDealerAnswered(
			$this->configurationOrderRepository->getByIdChecked($id)
		);
		$this->redirect('this');
	}


	public static function getConfigurationLinkParameters(ConfigurationOrder $configurationOrder): array
	{
		$parameters = [
			'v' => $configurationOrder->variant->id,
			'show' => 1,
		];
		$variants = [];
		$productInstances = [];
		foreach ($configurationOrder->items as $item) {
			$variants[] = $item->variant->id;
			if ($item->productInstance !== null && $item->productInstance->get) {
				$productInstances[] = $item->productInstance->id;
			}
		}
		if ($configurationOrder->code !== '') {
			$parameters['o'] = $configurationOrder->code;
		}
		if ($configurationOrder->productInstance !== null) {
			$productInstanceLocalization = $configurationOrder->productInstance->getLocalization($configurationOrder->mutation);
			if ($productInstanceLocalization instanceof ProductInstanceLocalization) {
				$parameters['productInstance'] = $productInstanceLocalization->id;
			}
		}
		return $parameters;
	}


}

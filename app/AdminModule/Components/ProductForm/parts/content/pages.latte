{var $props = [
	title: 'Stránky',
	id: 'pages',
	variant: 'main',
	icon: $templates.'/part/icons/file.svg',
	classes: ['u-mb-xxs'],
	rowMain: false,
	tags: [
		[text: 'Lokalizované'],
	]
]}

{embed $templates.'/part/box/toggle.latte', props=>$props, templates=>$templates}
	{block content}
		<div class="grid grid--center grid--x-0 grid--y-0">
			{var $gridSize = 12}
			{if $form['productLocalizations']->components->count() >= 2}
				{var $gridSize = 6}
			{/if}

			{foreach $form['productLocalizations']->components as $mutationId=>$localizationContainer}
				{var $mutation = $mutations->getById($mutationId)}
				{var $langCode = $mutation->langCode}

				{var $items = []}
				{foreach $localizationContainer['pages']->components as $pageKey=>$pageContainer}
					{continueIf $pageKey === 'newItemMarker'}

					{var $url = $urls['searchMutationPage']->toArray()}
					{php $url['params']['mutationId'] = $mutationId}

					{var $item = [
						inps: [
							[
								input: $pageContainer['name'],
								placeholder: 'Zadejte název stránky',
								data: [
									suggestinp-target: 'input',
								]
							],
							[
								input: $pageContainer['id'],
								data: [
									suggestinp-target: 'idInput',
								],
								classes: 'u-hide',
								type: 'hidden'

							]
						],
						btnsAfter: [
							[
								icon: $templates.'/part/icons/trash.svg',
								tooltip: 'Odstranit',
								variant: 'remove',
								data: [
									action: 'RemoveItem#remove'
								],
							]
						],
						data: [
							controller: 'RemoveItem SuggestInp',
							removeitem-target: 'item',
							suggestinp-target: 'wrapper',
							suggestinp-url-value: Nette\Utils\Json::encode($url),
						]

					]}
					{php $items[] = $item}
				{/foreach}


				{capture $localizedContent}
					{include $templates.'/part/box/list.latte',
						props: [
							data: [
								controller: 'List',
								List-name-value: 'page',
								List-mutationId-value: $mutation->id,

							],
							listData: [
								List-target: 'list',
							],
							addData: [
								action: 'List#add',
							],
							add: true,
							dragdrop: true,
							type: 'input',
							items: $items
						]
					}
				{/capture}

				{if $langCode == 'en'}
					<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
						<div class="row-main">
							<div class="tag u-mb-xxs">
								{$langCode}
							</div>
							{$localizedContent}
						</div>
					</div>
				{else}
					<div class="grid__cell size--{$gridSize}-12 size--lang js-lang js-lang--{$langCode|lower}">
						<div class="row-main">
							{include $templates.'/part/core/checkbox.latte',
								props: [
									label: '<span class="grid-inline"><span class="tag">'.strtoupper($langCode).'</span> <span>přebírá nastavení z výchozí verze</span></span>',
									input: $form['productLocalizations'][$mutation->id]['setup']['inheritPages'],
									dataInp: [
										controller:'ToggleCheckbox',
										action:'ToggleCheckbox#changeClass',
										togglecheckbox-target-value:'#checkbox-pages-'.strtolower($langCode),
										togglecheckbox-target-class-value:'is-open'
									],
								]
							}
							{var $isOpen = !($form['productLocalizations'][$mutation->id]['setup']['inheritPages']->getValue())}

							<div id="checkbox-pages-{$langCode|lower}" class="js-toggle-checkbox__content u-mt--xs {if $isOpen}is-open{/if}">
								{$localizedContent}
							</div>
						</div>
					</div>
				{/if}
			{/foreach}

		</div>
	{/block}
{/embed}

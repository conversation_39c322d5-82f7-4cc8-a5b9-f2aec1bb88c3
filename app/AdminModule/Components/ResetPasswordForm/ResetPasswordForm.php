<?php declare(strict_types = 1);

namespace App\AdminModule\Components\ResetPasswordForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\Model\Orm\Admin\AdminModel;
use App\Model\Orm\Orm;
use App\Model\Orm\User\UserModel;
use App\Model\Orm\UserHash\UserHashModel;
use App\Model\Translator;
use Nette\Application\UI;
use Nette\Bridges\ApplicationLatte\DefaultTemplate;
use Nette\Utils\ArrayHash;

/**
 * @property-read DefaultTemplate $template
 */
final class ResetPasswordForm extends UI\Control
{

	public function __construct(
		private readonly string $hash,
		private readonly Orm $orm,
		private readonly Translator $translator,
		private readonly AdminModel $adminModel,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly UserHashModel $userHashModel,
	) {}


	public function render(): void
	{
		$this->template->setTranslator($this->translator);
		$this->template->render(__DIR__ . '/resetPasswordForm.latte');
	}


	protected function createComponentForm(): UI\Form
	{
		$form = new UI\Form();
		$form->setTranslator($this->translator);
		$form->addHidden('hash', $this->hash);

		$form->addPassword('password', 'password')->setRequired();
		$form->addPassword('passwordVerify', 'password2')
			->setRequired()
			->addRule(UI\Form::EQUAL, 'form_password_not_same', $form['password']);

		$form->addSubmit('send', 'Send');

		// call method signInFormSucceeded() on success
		$form->onSuccess[] = [$this, 'formResetSuccess'];
		$form->onError[] = [$this, 'formResetError'];
		return $form;
	}

	public function formResetError(UI\Form $form): void
	{
		bd($form->errors);
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}


	public function formResetSuccess(UI\Form $form, ArrayHash $values): void
	{
		$valuesAll = $form->getHttpData();

		$hashObject = $this->orm->userHash->getBy(['hash' => $valuesAll['hash']]);
		$adminEntity = $hashObject->admin;

		if ($adminEntity !== null) {
			$this->adminModel->changePassword($adminEntity, $values->password);
			$this->userHashModel->useHash($hashObject);
			$this->flashMessage('form_reset_password', 'ok');
			$this->presenter->flashMessage('form_reset_password', 'ok');

			$form['password']->setValue('');
			$form['passwordVerify']->setValue('');
		} else {
			$form['password']->addError('user_not_found');
			$this->flashMessage('user_not_found', 'error');
		}

		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		} else {
			$this->presenter->redirect(':Admin:Sign:default');
		}
	}


	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

}

<?php declare(strict_types = 1);

namespace App\AdminModule\Components\Tree;

use App\Model\TreeStructure\TreeStructure;
use Closure;
use Nette\Application\UI\Control;

class Tree extends Control
{


	/**
	 * @phpstan-param Closure(): TreeStructure $getTreeStructureFunction
	 * @phpstan-param Closure(int $movedNodeId, int $targetNodeId, string $action): void $moveNodeFunction
	 * @phpstan-param Closure(int $parentId, string $name): void $createNodeFunction
	 * @phpstan-param Closure(int $id): string $linkFunction
	 */
	public function __construct(
		private readonly Closure $getTreeStructureFunction,
		private readonly Closure $moveNodeFunction,
		private readonly Closure $createNodeFunction,
		private readonly Closure $linkFunction,
		private readonly bool $isThickbox,
	)
	{
	}

	public function render(): void
	{
		$template = $this->template;

		$this->template->add('treeStructure', ($this->getTreeStructureFunction)());
		$this->template->add('moveParameterName', $this->getName() . '-' . 'moveId');
		$this->template->add('targetParameterName', $this->getName() . '-' . 'targetId');
		$this->template->add('actionParameterName', $this->getName() . '-' . 'action');
		$this->template->add('createParameterName', $this->getName() . '-' . 'create');
		$this->template->add('nameParameterName', $this->getName() . '-' . 'name');
		$this->template->add('isThickbox', $this->isThickbox);

		$template->render(__DIR__ . '/tree.latte');
	}

	public function handleMove(int $moveId = null, int $targetId = null, string $action = null): void
	{
		if ($moveId === null || $targetId === null || $action === null) {
			throw new \LogicException('Missing parameter for link');
		}

		($this->moveNodeFunction)($moveId, $targetId, $action);
	}

	public function handleCreate(int $targetId = null, string $name = null): void
	{
		if ($targetId === null || $name === null) {
			throw new \LogicException('Missing parameter for link');
		}

		($this->createNodeFunction)($targetId, $name);
	}

	public function handleLink(int $id): void
	{
		($this->linkFunction)($id);
	}
}

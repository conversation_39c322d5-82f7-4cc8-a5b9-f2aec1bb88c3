parameters:
	migrations:
		withDummyData: false

extensions:
	migrations: Nextras\Migrations\Bridges\NetteDI\MigrationsExtension

migrations:
	dir: %appDir%/../migrations # migrations base directory
	driver: mysql               # pgsql or mysql
	dbal: nextras               # nextras, nette, doctrine or dibi

	groups:
		core_structure:
			enabled: true
			directory: %appDir%/../migrations/core/structures
			dependencies: [state_structure]
			#generator: null
		core_basic_data:
			enabled: true
			directory: %appDir%/../migrations/core/basic-data
			dependencies: [core_structure, state_basic_data]
			#generator: null
		core_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/core/dummy-data
			dependencies: [core_basic_data]





		page_structure:
			enabled: true
			directory: %appDir%/../migrations/page/structures
			dependencies: [core_structure]
			#generator: null
		page_basic_data:
			enabled: true
			directory: %appDir%/../migrations/page/basic-data
			dependencies: [core_structure, page_structure]
			#generator: null

		state_structure:
			enabled: true
			directory: %appDir%/../migrations/state/structures
			dependencies: [core_structure]
		state_basic_data:
			enabled: true
			directory: %appDir%/../migrations/state/basic-data
			dependencies: [core_basic_data, state_structure]

		blog_structure:
			enabled: true
			directory: %appDir%/../migrations/blog/structures
			dependencies: [core_structure]
		blog_basic_data:
			enabled: true
			directory: %appDir%/../migrations/blog/basic-data
			dependencies: [page_structure, core_basic_data, blog_structure]
		blog_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/blog/dummy-data
			dependencies: [blog_basic_data]

		author_structure:
			enabled: true
			directory: %appDir%/../migrations/author/structures
			dependencies: [core_structure, blog_structure]
		author_basic_data:
			enabled: true
			directory: %appDir%/../migrations/author/basic-data
			dependencies: [page_structure, core_basic_data, author_structure, blog_basic_data]
		author_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/author/dummy-data
			dependencies: [author_basic_data]



		other_structure:
			enabled: true
			directory: %appDir%/../migrations/other/structures
			dependencies: [core_structure]
		other_basic_data:
			enabled: true
			directory: %appDir%/../migrations/other/basic-data
			dependencies: [other_structure, core_basic_data]

		user_structure:
			enabled: true
			directory: %appDir%/../migrations/user/structures
			dependencies: [core_structure]
		user_basic_data:
			enabled: true
			directory: %appDir%/../migrations/user/basic-data
			dependencies: [core_basic_data, user_structure]
		user_dummy_data:
			enabled: %migrations.withDummyData%
			directory: %appDir%/../migrations/user/dummy-data
			dependencies: [user_basic_data]

		emailTemplate_structure:
			enabled: true
			directory: %appDir%/../migrations/emailTemplate/structures
			dependencies: [core_structure]
		emailTemplate_basic_data:
			enabled: true
			directory: %appDir%/../migrations/emailTemplate/basic-data
			dependencies: [core_basic_data, emailTemplate_structure]

		parameter_structure:
			enabled: true
			directory: %appDir%/../migrations/parameter/structures
			dependencies: [core_structure]
		parameter_basic_data:
			enabled: true
			directory: %appDir%/../migrations/parameter/basic-data
			dependencies: [core_basic_data, parameter_structure]

		product_structure:
			enabled: true
			directory: %appDir%/../migrations/product/structures
			dependencies: [core_structure]
		product_basic_data:
			enabled: true
			directory: %appDir%/../migrations/product/basic-data
			dependencies: [core_basic_data, product_structure]

		stock_structure:
			enabled: true
			directory: %appDir%/../migrations/stock/structures
			dependencies: [core_structure]
		stock_basic_data:
			enabled: true
			directory: %appDir%/../migrations/stock/basic-data
			dependencies: [core_basic_data, stock_structure]

		seolink_structure:
			enabled: true
			directory: %appDir%/../migrations/seolink/structures
			dependencies: [core_structure]

		configurationOrder_structure:
			enabled: true
			directory: %appDir%/../migrations/configurationOrder/structures
			dependencies: [core_structure]

